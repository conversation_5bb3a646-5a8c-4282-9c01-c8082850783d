{"meters": [{"name": "beacon_server_ios_place_order_success_rate", "description": "Success rate for iOS PlaceOrderProdTime events in beacon-server", "type": "gauge", "unit": "percent", "query": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=PlaceOrderProdTime | dedup serviceLog.userId | rename serviceLog.appContext.backend_error_code as errorCode | eval errorCodeChain=if(isnull(errorCode), \"success\", \"error\") | stats count(eval(errorCodeChain=\"success\")) as successCount count(eval(errorCodeChain=\"error\")) as errorsCount, count as totalEvents | eval rate = (successCount/(successCount+errorsCount))*100 | fields rate", "tags": {"service": "beacon-server", "platform": "iOS", "event": "PlaceOrderProdTime", "metric_type": "success_rate"}}, {"name": "beacon_server_ios_place_order_success_count", "description": "Count of successful iOS PlaceOrderProdTime events in beacon-server", "type": "counter", "unit": "count", "query": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=PlaceOrderProdTime | dedup serviceLog.userId | rename serviceLog.appContext.backend_error_code as errorCode | eval errorCodeChain=if(isnull(errorCode), \"success\", \"error\") | stats count(eval(errorCodeChain=\"success\")) as successCount | fields successCount", "tags": {"service": "beacon-server", "platform": "iOS", "event": "PlaceOrderProdTime", "metric_type": "success_count"}}, {"name": "beacon_server_ios_place_order_error_count", "description": "Count of failed iOS PlaceOrderProdTime events in beacon-server", "type": "counter", "unit": "count", "query": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=PlaceOrderProdTime | dedup serviceLog.userId | rename serviceLog.appContext.backend_error_code as errorCode | eval errorCodeChain=if(isnull(errorCode), \"success\", \"error\") | stats count(eval(errorCodeChain=\"error\")) as errorsCount | fields errorsCount", "tags": {"service": "beacon-server", "platform": "iOS", "event": "PlaceOrderProdTime", "metric_type": "error_count"}}, {"name": "beacon_server_ios_place_order_total_events", "description": "Total count of iOS PlaceOrderProdTime events in beacon-server (deduplicated by userId)", "type": "counter", "unit": "count", "query": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=PlaceOrderProdTime | dedup serviceLog.userId | stats count as totalEvents | fields totalEvents", "tags": {"service": "beacon-server", "platform": "iOS", "event": "PlaceOrderProdTime", "metric_type": "total_count"}}], "metadata": {"source": "splunk", "index": "sfly-prod-servicesplatform-audit", "service": "beacon-server", "platform": "iOS", "event_type": "PlaceOrderProdTime", "deduplication": "serviceLog.userId", "created_date": "2025-01-04", "description": "Metrics for tracking iOS place order events in beacon-server service, including success rate, counts, and error tracking"}}