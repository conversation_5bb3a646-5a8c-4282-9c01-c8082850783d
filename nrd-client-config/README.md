# nrd-client-config

This repository holds the client configuration files that the Beacon Server (also known as NRD) uses to send client application information to both Splunk and SignalFx.

# Overview

Beacon Server allows external applications (mobile applications or web pages) to submit data records to Shutterfly that are logged into Splunk and optionally used to create metrics.  For more information about Beacon Server, see https://snapfish-llc.atlassian.net/wiki/spaces/SPC/pages/2743999609/NRD.

Client configuration files are stored in the [client-config](client-config) folder.  The definition of the client configuration files is documented at https://snapfish-llc.atlassian.net/wiki/spaces/SPC/pages/2744000631/NRD+Beacon+Server+Meter+Configuration+JSON.

# Adding new Beacon Server client configuration

* Make sure your client configuration is correct. See the existing client configurations for your reference.
* Add new client configuration file inside [client-config](client-config) folder.
* Add correct details for `appName` ,`App` , `ManagedBy` and `ManagedByContact`.
* Open a pull request with your change.

**NOTE: Share your pull request details in the #nrd Slack channel for review so Developer Platform team will review the pull request.**


## Pull request review process 

- Depending on your configuration file, SignalFx metrics may be created from the ServiceLog records sent in. If this is a new NRD client and you intend to leverage SignalFx metrics, please include a comment in the PR description requesting SignalFx tokens be created. Provide an estimate of how many metrics expect to be created in prod. Metrics should not include any highly-discriminated data such as a userId or non-patterned URLs in any metric to keep the metric MTS (distinct metric names and values) limited.

- NRD has a client-specific meter registry for NRD to segregate the metrics for each client and set limitations per client.
The Developer Platform team will request the SignalFx tokens and configure NRD to accept them.

- The Developer Platform team will approve your PR after the SignalFx tokens are created and NRD is configured to accept them.

- Once the PR is approved, merge your application changes.

- After that you can deploy your client configurations as detailed below. The change would apply shortly (after cache expires).  Test your application changes to verify logs and metrics created from the ServiceLog records sent to NRD work as expected.

## Build Status

Run the below Jenkins jobs to deploy your client configuration file checked into this repo to Beacon Server in the indicated environment.

* [![Build Status](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-dev/badge/icon)](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-dev/) Deploy (Dev)
* [![Build Status](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-beta/badge/icon)](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-beta/) Deploy (Beta)
* [![Build Status](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-stage/badge/icon)](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-stage/) Deploy (Stage)
* [![Build Status](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-prod/badge/icon)](https://build.internal.shutterfly.com/job/nrd-client-config/job/nrd-client-config-deploy-prod/) Deploy (Prod)


## SignalFx dashboards

* Create or modify your SignalFx dashboards for the new metrics produced by your client.

* Refer SignalFx [dashboard](https://shutterfly.signalfx.com/#/dashboard/E1IxlygAYAA?groupId=EZDkXHeAYAA&configId=E1Ixl0BAcAE) for environment specific MTS usages for per-client.
