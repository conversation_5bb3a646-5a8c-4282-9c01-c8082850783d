#!/bin/sh
#if appN<PERSON> is empty process all files
set -vx
WORKSPACE=$1
url=$2
appNameInput=$4
configDirectoryPrefix=$3

configDirectorySuffix="client-config"

echo "value of client clientConfigDirectory : ${configDirectoryPrefix}"

if [ -z "$appNameInput" ]; then
  #process all files
  for file in ${WORKSPACE}/${configDirectorySuffix}/${configDirectoryPrefix}/*; do
    curl -k -X POST --header 'Content-Type: application/json' --header 'Accept: */*' -d @$file ${url}
  done
else

  found=0
  for file in ${WORKSPACE}/${configDirectorySuffix}/${configDirectoryPrefix}/*; do
    #extract app name from client config file
    appNameFromJson=$(python ${WORKSPACE}/py/readjson.py $file 2>&1 >/dev/null)
    if [ "$appNameFromJson" == "$appNameInput" ]; then
      response=`curl -k -X POST --header 'Content-Type: application/json' --header 'Accept: */*' -d @$file ${url} | grep "success"`
      result=`echo $response | grep "success"`
        if [ -z "$result" ]; then
          echo "Deployment did not succeed. Check the error"
          exit 1
        fi
      found=1
      break
    fi
  done
  if [ "$found" == "0" ]; then
    echo "Did not find file $appNameInput.json"
    exit 1
  fi
fi
