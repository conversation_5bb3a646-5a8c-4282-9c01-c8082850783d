{"appName": "Na<PERSON><PERSON>", "commonDimensions": {"App": "Na<PERSON><PERSON>", "ManagedByContact": "<EMAIL>", "success": "$.appContext.success"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "dimensions": {"productType": "$.appContext.productType"}}, {"path": "$[?((@.eventId=='projectLoad' || @.eventId=='internalSaveUserProject.result') && (@.appContext.device == 'desktop'))]", "meterType": "counter", "meterName": "event.projectLoad_and_internalSaveUserProject_result", "dimensions": {"success": "$.appContext.success", "eventId": "$.eventId"}}]}