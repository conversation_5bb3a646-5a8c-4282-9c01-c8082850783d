{"appName": "Mobile", "commonDimensions": {"App": "Mobile", "ManagedBy": "Mobile", "ManagedByContact": "<EMAIL>"}, "meters": [{"path": "$[?((@.eventId == 'PlaceOrderProdTime') && (@.appContext.platform == 'iOS'))]", "meterType": "counter", "meterName": "event.PlaceOrderProdTime.ios", "dimensions": {"backend_error_code": "$.appContext.backend_error_code"}, "splunkQuery": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=PlaceOrderProdTime | dedup serviceLog.userId | rename serviceLog.appContext.backend_error_code as errorCode | eval errorCodeChain=if(isnull(errorCode), \"success\", \"error\") | stats count(eval(errorCodeChain=\"success\")) as successCount count(eval(errorCodeChain=\"error\")) as errorsCount, count as totalEvents | eval rate = (successCount/(successCount+errorsCount))*100"}, {"path": "$[?((@.eventId == 'SFGNetworkEvent') && (@.appContext.platform == 'iOS') && (@.reqContext.uri =~ /.*catalog\\/screen.*/))]", "meterType": "counter", "meterName": "event.SFGNetworkEvent.ios.catalog.screen", "dimensions": {"status": "$.resContext.status"}, "splunkQuery": "index=sfly-prod-servicesplatform-audit serviceLog.appContext.platform=iOS serviceLog.eventId=SFGNetworkEvent \"serviceLog.reqContext.uri\"=\"*catalog/screen*\" \"serviceLog.resContext.status\"!=504 | dedup serviceLog.userId | rename serviceLog.appContext.* as * | rename serviceLog.resContext.status as httpcode | stats count as total, count(eval(httpcode > 0 and httpcode < 400)) as success | eval percent=(success/total)*100 | eval error=(total-success) | eval percent=if(success < 20 and error < 20, 100, percent)"}, {"path": "$[?((@.eventId == 'CartCheckoutProdTime') && (@.appContext.platform == 'iOS'))]", "meterType": "counter", "meterName": "event.CartCheckoutProdTime.ios", "dimensions": {"errorCode": "$.appContext.errorCode"}, "splunkQuery": "index=sfly-prod-servicesplatform-audit SERVICE=\"beacon-server\" serviceLog.appContext.platform=iOS serviceLog.eventId=CartCheckoutProdTime | rename serviceLog.appContext.errorCode as errorCode | dedup serviceLog.userId | stats count(eval(isnull(errorCode))) as successEvents, count as totalEvents | eval rate = (successEvents/totalEvents)*100"}, {"path": "$[?((@.eventId == 'CategoryImageProdLoadTime') && (@.appContext.platform == 'iOS') && (@.appContext.productID) && (@.appContext.imageSizekByte > 2000))]", "meterType": "counter", "meterName": "event.CategoryImageProdLoadTime.ios.productID", "dimensions": {"productID": "$.appContext.productID", "imageSizekByte": "$.appContext.imageSizekByte"}, "splunkQuery": "index=sfly-prod-servicesplatform-* serviceLog.appContext.platform=iOS serviceLog.eventId=CategoryImageProdLoadTime productID \"serviceLog.appContext.imageSizekByte\" > 2000 | dedup serviceLog.appContext.productID | stats count as assets"}, {"path": "$[?((@.eventId == 'CategoryImageProdLoadTime') && (@.appContext.platform == 'iOS') && (@.appContext.subCategoryID) && (@.appContext.imageSizekByte > 2000))]", "meterType": "counter", "meterName": "event.CategoryImageProdLoadTime.ios.subCategoryID", "dimensions": {"subCategoryID": "$.appContext.subCategoryID", "imageSizekByte": "$.appContext.imageSizekByte"}, "splunkQuery": "index=sfly-prod-servicesplatform-* serviceLog.appContext.platform=iOS serviceLog.eventId=CategoryImageProdLoadTime subCategoryID \"serviceLog.appContext.imageSizekByte\" > 2000 | dedup serviceLog.appContext.subCategoryID | stats count as assets"}, {"path": "$[?((@.eventId == 'SFGNetworkEvent') && (@.appContext.platform == 'iOS') && (@.reqContext.uri =~ /.*catalog\\/screens\\/home.*/))]", "meterType": "counter", "meterName": "event.SFGNetworkEvent.ios.catalog.screens.home", "dimensions": {"status": "$.resContext.status"}, "splunkQuery": "index=sfly-prod-servicesplatform-audit serviceLog.appContext.platform=iOS serviceLog.eventId=SFGNetworkEvent \"serviceLog.reqContext.uri\"=\"*catalog/screens/home*\" \"serviceLog.resContext.status\"!=504 | dedup serviceLog.userId | rename serviceLog.appContext.* as * | rename serviceLog.resContext.status as httpcode | stats count as total, count(eval(httpcode > 0 and httpcode < 400)) as success | eval percent=(success/total)*100 | eval error=(total-success) | eval percent=if(success < 20 and error < 20, 100, percent)"}]}