{"appName": "pip-data-service", "commonDimensions": {"App": "pip-data-service", "ManagedBy": "Commerce-store", "ManagedByContact": "<EMAIL>", "Environment": "$.appContext.environment"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "dimensions": {"canary": "$.appContext.canary", "downstream_response": "$.appContext.downstreamResponse"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.cns_service.get_product_config.response.notcached')]", "meterName": "upstream.cns_service.get_product_config.response.notcached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.cns_service.get_product_config.response.cached')]", "meterName": "upstream.cns_service.get_product_config.response.cached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.csblueprint.fetch_bundle_type.response.notcached')]", "meterName": "upstream.csblueprint.fetch_bundle_type.response.notcached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.csblueprint.fetch_bundle_type.response.cached')]", "meterName": "upstream.csblueprint.fetch_bundle_type.response.cached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.get_product.response.notcached')]", "meterName": "upstream.get_product.response.notcached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.get_product.response.cached')]", "meterName": "upstream.get_product.response.cached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.thumbnail.get_image_url.response.notcached')]", "meterName": "upstream.thumbnail.get_image_url.response.notcached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.thumbnail.get_image_url.response.cached')]", "meterName": "upstream.thumbnail.get_image_url.response.cached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.creationpath.get_product_config.response.cached')]", "meterName": "upstream.creationpath.get_product_config.response.cached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}, {"path": "$.logEvents[?(@.eventType == 'upstream.creationpath.get_product_config.response.notcached')]", "meterName": "upstream.creationpath.get_product_config.response.notcached", "meterType": "counter", "dimensions": {"canary": "$.appContext.canary", "method_name": "$.appContext.methodName"}}]}