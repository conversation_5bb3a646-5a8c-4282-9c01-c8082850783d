{"appName": "store-fe", "commonDimensions": {"App": "store-fe", "ManagedBy": "Store-FE", "ManagedByContact": "<EMAIL>", "platform": "$.appContext.platform", "device": "$.appContext.device", "os": "$.appContext.os", "browser": "$.appContext.browser", "success": "$.appContext.success", "pageType": "$.appContext.pageType", "productType": "$.appContext.productType", "source": "$.appContext.source", "httpStatus": "$.appContext.httpStatus"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "percentiles": [0.95]}, {"path": "$[?(@.eventId=='store-fe-dependent-api-metric')]", "meterType": "counter", "meterName": "store-fe-dependent-api-metric"}]}