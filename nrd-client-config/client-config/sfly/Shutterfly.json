{"appName": "Shutterfly", "commonDimensions": {"App": "Shutterfly", "ManagedBy": "Mobile", "ManagedByContact": "<EMAIL>"}, "meters": [{"path": "$[?((@.eventId == 'SFGNetworkEvent') && (@.appContext.platform == 'iOS') && ((@.reqContext.uri =~ /https:\\/\\/uniup2\\.shutterfly\\.com\\/upload\\/.*$/) || (@.reqContext.uri =~ /https:\\/\\/up2\\.shutterfly\\.com\\/upload\\/.*$/) || (@.reqContext.uri =~ /https:\\/\\/uniup\\.shutterfly\\.com\\/upload\\/.*$/)))]", "meterType": "counter", "meterName": "event.SFGNetworkEvent.ios.upload", "dimensions": {"status": "$.resContext.status"}}, {"path": "$[?((@.eventId == 'CartCheckoutProdTime') && (@.appContext.platform == 'iOS' ))]", "meterType": "counter", "meterName": "event.CartCheckoutProdTime", "dimensions": {"errorCode": "$.appContext.errorCode"}}, {"path": "$[?((@.eventId == 'PlaceOrderProdTime') && (@.appContext.platform == 'iOS' ))]", "meterType": "counter", "meterName": "event.PlaceOrderProdTime", "dimensions": {"backend_error_code": "$.appContext.backend_error_code"}}, {"path": "$[?((@.eventId == 'MediaUploadFinished') && (@.appContext.platform == 'Android' ))]", "meterType": "counter", "meterName": "event.MediaUploadFinished", "dimensions": {"result": "$.appContext.result", "uploadType": "$.appContext.uploadType"}}, {"path": "$[?((@.eventId == 'checkout_performance_report') && (@.appContext.platform == 'Android' ))]", "meterType": "counter", "meterName": "event.checkout_performance_report", "dimensions": {"is_checkout_successfully": "$.appContext.is_checkout_successfully"}}, {"path": "$[?((@.eventId == 'orderRetryAbleSession') && (@.appContext.platform == 'Android' ))]", "meterType": "counter", "meterName": "event.orderRetryAbleSession", "dimensions": {"retry_error_count": "$.appContext['retry error count']"}}]}