{"appName": "cns-client", "commonDimensions": {"App": "cns-client", "ManagedBy": "CGD", "ManagedByContact": "<EMAIL>", "platform": "$.appContext.platform"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "percentiles": [0.9], "dimensions": {"productType": "$.appContext.productType"}}, {"path": "$.[?(@.logEvents)]logEvents[?(@.eventType == 'path.layout.change')]", "meterType": "counter", "meterName": "path.layout.change", "dimensions": {"layoutID": "$.appContext.layoutID", "layoutIntID": "$.appContext.layoutIntID", "layoutVersion": "$.appContext.layoutVersion", "layoutName": "$.appContext.layoutName"}}, {"path": "$.[?(@.logEvents)]logEvents[?(@.eventType == 'invalid.sku')]", "meterType": "counter", "meterName": "invalid.sku"}, {"path": "$.[?(@.logEvents)]logEvents[?(@.eventType =~ /.*\\.error/)]", "meterType": "counter", "meterName": "cns.error", "dimensions": {"eventType": "$.logEvents[0].eventType", "errorName": "$.appContext.errorName", "productType": "$.appContext.productType"}}]}