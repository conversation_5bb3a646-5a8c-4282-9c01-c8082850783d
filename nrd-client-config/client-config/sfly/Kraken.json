{"appName": "<PERSON><PERSON><PERSON>", "commonDimensions": {"App": "<PERSON><PERSON><PERSON>", "ManagedBy": "Photos Experience", "ManagedByContact": "<EMAIL>", "appVersion": "$.AppVersion", "success": "$.appContext.success"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "dimensions": {"filetype": "$.appContext.type", "errorStatus": "$.appContext.errorStatus", "inactive_retention_days": "$.appContext.retentionDays"}, "percentiles": [0.5, 0.8, 0.95]}, {"path": "$[?(@.eventId=='social_error')]", "meterType": "counter", "meterName": "event.social_error", "dimensions": {"source": "$.appContext.source", "browser": "$.appContext.browser", "os": "$.appContext.os", "client": "$.appContext.clientId", "device": "$.appContext.device", "platform_version": "$.appContext.platform_version"}}, {"path": "$.logEvents[?(@.eventType =='browserName')]", "meterType": "counter", "meterName": "browserName", "dimensions": {"source": "$.appContext.browser"}}, {"path": "$.logEvents[?(@.eventType =='OStype')]", "meterType": "counter", "meterName": "OStype", "dimensions": {"source": "$.appContext.ostype"}}, {"path": "$.logEvents[?(@.eventType =='isVideo')]", "meterType": "counter", "meterName": "isVideo", "dimensions": {"source": "$.appContext.isVideo"}}, {"path": "$.logEvents[?(@.eventType =='photos_source')]", "meterType": "counter", "meterName": "Photos_Source", "dimensions": {"source": "$.appContext.source"}}, {"path": "$[?((@.eventId=='process_moment'))]", "meterType": "counter", "meterName": "event.process_moment", "dimensions": {"process_status": "$.appContext.process_status", "browser": "$.appContext.browser", "os": "$.appContext.os", "client": "$.appContext.clientId", "device": "$.appContext.device", "is_video": "$.appContext.isVideo", "source": "$.appContext.source_type", "platform_version": "$.appContext.platform_version"}}, {"path": "$[?((@.eventId=='process_session_start'))]", "meterType": "counter", "meterName": "event.process_session_start", "dimensions": {"browser": "$.appContext.browser", "os": "$.appContext.os", "client": "$.appContext.clientId", "device": "$.appContext.device", "only_videos": "$.appContext.onlyVideos", "contains_videos": "$.appContext.containsVideos", "platform_version": "$.appContext.platform_version", "upload_only": "$.appContext.upload_only", "import_only": "$.appContext.import_only"}}, {"path": "$[?(( @.eventId=='process_session_end'))]", "meterType": "counter", "meterName": "event.process_session_end", "dimensions": {"upload_status": "$.appContext.upload_status", "browser": "$.appContext.browser", "os": "$.appContext.os", "client": "$.appContext.clientId", "device": "$.appContext.device", "only_videos": "$.appContext.onlyVideos", "contains_videos": "$.appContext.containsVideos", "import_status": "$.appContext.import_status", "platform_version": "$.appContext.platform_version", "source": "$.appContext.source_type", "upload_only": "$.appContext.upload_only", "import_only": "$.appContext.import_only"}}]}