{"appName": "tokenization-client", "commonDimensions": {"App": "tokenization-client", "ManagedBy": "Commerce", "ManagedByContact": "<EMAIL>", "success": "$.appContext.success"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "percentiles": [0.5, 0.8, 0.9]}, {"path": "$.logEvents[?(@.eventType =~ /.*Warning$/)]", "meterType": "counter", "meterName": "tokenization-client.warning", "dimensions": {"message": "$.logEvents[0].message"}}, {"path": "$.logEvents[?(@.eventType =~ /.*Info$/)]", "meterType": "counter", "meterName": "tokenization-client.info", "dimensions": {"eventType": "$.logEvents[0].eventType", "message": "$.logEvents[0].message"}}, {"path": "$.logEvents[?(@.eventType =~ /.*Error$/)]", "meterType": "counter", "meterName": "tokenization-client.error", "dimensions": {"eventType": "$.logEvents[0].eventType"}}]}