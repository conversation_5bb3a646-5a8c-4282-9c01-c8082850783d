{"appName": "example-app", "commonDimensions": {"App": "example-app", "ManagedBy": "Services Platform", "ManagedByContact": "<EMAIL>", "platform": "$.appContext.platform", "productType": "$.appContext.productType"}, "meters": [{"path": "$.perfSummary", "meterType": "timer"}, {"path": "$.logEvents[?(@.eventType == 'FIND_TIMER')]", "meterType": "timer", "meterName": "FIND_TIMER", "value": "eventTimeInMillis", "dimensions": {"layoutID": "$.appContext.layoutID"}}, {"path": "$.logEvents[?(@.eventType == 'example.path.layout.change')]", "meterType": "counter", "meterName": "example.path.layout.change", "dimensions": {"layoutID": "$.appContext.layoutID", "layoutIntID": "$.appContext.layoutIntID", "layoutVersion": "$.appContext.layoutVersion", "layoutName": "$.appContext.layoutName"}}, {"path": "$.logEvents[?(@.eventType == 'example.sku')]", "meterType": "counter", "meterName": "example.sku"}, {"path": "$.logEvents[?(@.eventType =~ /.*\\.error/)]", "meterType": "counter", "meterName": "example.error", "dimensions": {"eventType": "$.logEvents[0].eventType", "errorName": "$.appContext.errorName"}}]}