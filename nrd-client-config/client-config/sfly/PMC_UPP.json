{"appName": "PMC_UPP", "commonDimensions": {"App": "PMC_UPP", "ManagedBy": "Photos Experience", "ManagedByContact": "<EMAIL>"}, "meters": [{"path": "$[?((@.eventId == 'upload_session_start' || @.eventId == 'upload_session_end' || @.eventId == 'upload_photo') && !(@.appContext.user_os == 'ios' || @.appContext.user_os == 'android'))]", "meterType": "counter", "meterName": "event.upload_session_and_photo", "dimensions": {"upload_status": "$.appContext.upload_status", "eventId": "$.eventId"}}]}