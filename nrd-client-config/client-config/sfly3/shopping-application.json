{"appName": "shopping-application", "commonDimensions": {"App": "shopping-application", "ManagedBy": "SFLY-Shopping", "ManagedByContact": "<EMAIL>", "platform": "$.appContext.platform", "device": "$.appContext.device", "os": "$.appContext.os", "browser": "$.appContext.browser", "success": "$.appContext.success", "pageType": "$.appContext.pageType", "productType": "$.appContext.productType", "source": "$.appContext.source", "httpStatus": "$.appContext.httpStatus"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "percentiles": [0.95]}, {"path": "$[?(@.eventId=='shopping-application-dependent-api-metric')]", "meterType": "counter", "meterName": "shopping-application-dependent-api-metric"}]}