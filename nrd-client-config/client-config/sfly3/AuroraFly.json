{"appName": "AuroraFly", "commonDimensions": {"App": "AuroraFly", "ManagedBy": "Mobile", "ManagedByContact": "<EMAIL>"}, "meters": [{"path": "$.[?(@.appContext)]appContext[?($.eventId=='platform.performance.app_launch')]", "meterName": "sfly3.mobile.event.app.launch.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='shopping.performance.load_content')]", "meterName": "sfly3.mobile.event.shopping.loadcontent.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='catalog.performance.open_category')]", "meterName": "sfly3.mobile.event.category.open.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='catalog.performance.open_tn_page')]", "meterName": "sfly3.mobile.event.thumbnail.open.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='catalog.performance.search')]", "meterName": "sfly3.mobile.event.catalog.search.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$[?((@.eventId == 'SFGNetworkEvent') && (@.reqContext.uri =~ /.*productdetails.*/))]", "meterType": "counter", "meterName": "sfly3.mobile.event.SFGNetworkEvent.pip.request.count", "dimensions": {"status": "$.resContext.status", "platform": "$.appContext.platform"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='catalog.performance.open_product')]", "meterName": "sfly3.mobile.event.catalog.open_product.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$[?(@.eventId == 'creation_path.setup')]", "meterType": "counter", "meterName": "sfly3.mobile.event.creationpath.setup.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "success": "$.appContext.success"}}, {"path": "$[?((@.eventId =~ /auth.*/))]", "meterType": "counter", "meterName": "sfly3.mobile.event.auth.signin.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform"}}, {"path": "$[?((@.eventId =~ /api.thislife.*/))]", "meterType": "counter", "meterName": "sfly3.mobile.event.api.thislife.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform"}}, {"path": "$[?(@.eventId == 'SFGNetworkEvent' && @.reqContext.uri =~ /https:\\/\\/uniup2\\.shutterfly\\.com\\/upload\\/npc\\/.*$/)]", "meterType": "counter", "meterName": "sfly3.mobile.event.SFGNetworkEvent.upload.npc.count", "dimensions": {"status": "$.resContext.status", "platform": "$.appContext.platform"}}, {"path": "$[?(@.eventId == 'SFGNetworkEvent' && @.reqContext.uri =~ /https:\\/\\/uniup2\\.shutterfly\\.com\\/upload\\/pc\\/.*$/)]", "meterType": "counter", "meterName": "sfly3.mobile.event.SFGNetworkEvent.upload.pc.count", "dimensions": {"status": "$.resContext.status", "platform": "$.appContext.platform"}}, {"path": "$[?(@.eventId == 'upload.nonproduct.session.end')]", "meterType": "counter", "meterName": "sfly3.mobile.event.upload.nonproduct.session.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "state": "$.appContext.state"}}, {"path": "$[?(@.eventId == 'upload.nonproduct.autosession.end')]", "meterType": "counter", "meterName": "sfly3.mobile.event.upload.nonproduct.autosession.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "state": "$.appContext.state"}}, {"path": "$[?(@.eventId == 'upload.product.session.end')]", "meterType": "counter", "meterName": "sfly3.mobile.event.upload.product.session.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "state": "$.appContext.state"}}, {"path": "$[?(@.eventId == 'creation_path.add_to_cart')]", "meterType": "counter", "meterName": "sfly3.mobile.event.add2cart.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "success": "$.appContext.success"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='creation_path.add_to_cart')]", "meterName": "sfly3.mobile.event.add2cart.duration", "meterType": "timer", "value": "duration", "dimensions": {"device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$[?(@.eventId == 'checkout.checkoutAction')]", "meterType": "counter", "meterName": "sfly3.mobile.event.checkout.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "success": "$.appContext.success"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='checkout.cartCheckoutTiming')]", "meterName": "sfly3.mobile.event.cartCheckout.duration", "meterType": "timer", "value": "duration", "dimensions": {"eventId": "$.eventId", "device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}, {"path": "$[?(@.eventId == 'checkout.placeOrderAction')]", "meterType": "counter", "meterName": "sfly3.mobile.event.placeorder.request.count", "dimensions": {"eventId": "$.eventId", "platform": "$.appContext.platform", "success": "$.appContext.success"}}, {"path": "$.[?(@.appContext)]appContext[?($.eventId=='checkout.placeOrderTiming')]", "meterName": "sfly3.mobile.event.placeorder.duration", "meterType": "timer", "value": "duration", "dimensions": {"eventId": "$.eventId", "device": "$.appContext.device", "platform": "$.appContext.platform", "connection": "$.appContext.connection"}}]}