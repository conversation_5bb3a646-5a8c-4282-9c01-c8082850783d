{"appName": "builderapp3-client", "commonDimensions": {"App": "builderapp3-client", "ManagedBy": "SFLY-Builder", "ManagedByContact": "<EMAIL>", "platform": "$.appContext.platform", "device": "$.appContext.device", "os": "$.appContext.os", "browser": "$.appContext.browser", "success": "$.appContext.success"}, "meters": [{"path": "$.perfSummary", "meterType": "timer", "percentiles": [0.5, 0.75, 0.95]}, {"path": "$[?(@.eventId=='userAction')]", "meterType": "counter", "meterName": "event.userAction", "dimensions": {"userActionName": "$.appContext.userActionName", "actionType": "$.appContext.type", "productType": "$.appContext.BUILDER_TYPE"}}, {"path": "$[?(@.eventId =~ /^MagicLabsApiRequest.*/)]", "meterType": "counter", "meterName": "event.magicLabsApiRequest", "dimensions": {"actionName": "$.appContext.actionName", "apiMethod": "$.appContext.apiMethod"}}]}