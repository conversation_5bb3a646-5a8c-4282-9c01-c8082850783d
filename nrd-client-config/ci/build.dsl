import Defs
import policies.sfly.SflyDefaults
import policies.DockerImages
import policies.Defaults

import static Defs.*

for ( deploymentConfigVariant in ECS_Configurations ) {
    for (env in deploymentConfigVariant.envConfigUrlMap.keySet()) {
        freeStyleJob("${Defs.appName}-deploy-${deploymentConfigVariant.name}-${env}") {
            description("Upload client config files to REDIS in ${deploymentConfigVariant.name}")
            authorization {
                for (currentUser in Defs.devTeam) {
                    permissionAll(currentUser)
                }
                permission('hudson.model.Item.Build', 'authenticated')
            }
            scm {
                SflyDefaults.git(delegate, Defs.repoUrl, "*/\${Branch}")
            }

            parameters {
                stringParam('Branch', 'master', '')
                stringParam('appNameInput', '', 'Enter filename; e.g. foo deploys foo.json. Empty to upload all client config files to redis')
                if(env == 'prod') {
                    stringParam('CCP', '', 'Service-now ticket required for production')
                }
                stringParam('deployAll', '', 'Enter true to confirm deploying all client config files. ')
            }


            steps {
                if(env == 'prod') {
                    shell('requireCCP \${CCP}')
                }
                shell("""\
                    if [ "\${appNameInput}" = "" ] && [ "\${deployAll}" != "true" ]; then
                        echo "you must enter 'true' for deployAll to deploy all scripts"
                        exit 1
                    fi
                """)
                shell("""\
                    chmod a+x \${WORKSPACE}/script/build.sh

                    \${WORKSPACE}/script/build.sh \${WORKSPACE} ${deploymentConfigVariant.envConfigUrlMap.get(env)} ${deploymentConfigVariant.name} \${appNameInput}
                """)
            }

            SflyDefaults.dockerImage(delegate, DockerImages.JENKINS_SLAVE_DOCKER_DEPLOY)
            Defaults.dockerFile(delegate, 'ci', 'Dockerfile')
        }

    } // end env loop


} // end for loop