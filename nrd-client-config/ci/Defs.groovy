import policies.Teams
import policies.Environments

class Defs {
    public static final def appName = "nrd-client-config"
    public static final def devTeam = Teams.DEVELOPER_PLATFORM.members
    public static final def repoUrl = "**************:sflyinc-shutterfly/nrd-client-config.git"
    public static final def configurePermissions = [
            'hudson.model.Item.Delete',
            'hudson.model.Item.Configure',
            'hudson.model.Item.Read',
            'hudson.model.Item.ExtendedRead',
            'hudson.model.Item.Discover',
            'hudson.model.Item.Build',
            'hudson.model.Item.Workspace',
            'hudson.model.Item.Cancel',
            'hudson.model.Run.Delete',
            'hudson.model.Run.Update',
            'hudson.scm.SCM.Tag' ]


    /**
     * Struct-type class to help manage the different deployments (Sfly3 and sfly)
     */
    static class DeploymentVariantConfigurations {
        String name
        Map<String, String> envConfigUrlMap

        Environments[] envs

        DeploymentVariantConfigurations(String name, Environments[] envs) {
            this.name = name
            this.envs = envs
            envConfigUrlMap = new HashMap<>()
            for (env in envs){
                envConfigUrlMap.put(env.value, getConfigEndpoint(name ,env.value))
            }
        } // constructor end

        static def String getConfigEndpoint(String name, String env){
            def environmentPlaceholder = env
            def configPlaceholder = name
            def awsAccountPlaceholder = "dev"
            if (env == "stage"){
                awsAccountPlaceholder = "preprod"
            } else if (env == "prod"){
                awsAccountPlaceholder = "prod"
            }
            def baseUrl = "https://beacon-server-internal.${environmentPlaceholder}.us-east-1.${configPlaceholder}-aws-${awsAccountPlaceholder}.sfly.int/_internal/nrd/client_config"
            if (name == "sfly3" || name == "snapfish"){
               baseUrl = baseUrl.replace("-internal", "")
            }
            return baseUrl
        }
    }

    public static final def ECS_Configurations = [
            // sfly configs
            new DeploymentVariantConfigurations(
                    "sfly",
                    [Environments.DEV, Environments.BETA, Environments.STAGE, Environments.PROD] as Environments[]),

            // sfly3 configs
            new DeploymentVariantConfigurations(
                    "sfly3",
                    [Environments.DEV, Environments.STAGE, Environments.PROD] as Environments[]),

            // snapfish configs
            new DeploymentVariantConfigurations(
                    "snapfish",
                    [Environments.DEV, Environments.STAGE, Environments.PROD] as Environments[]),
    ]
}
