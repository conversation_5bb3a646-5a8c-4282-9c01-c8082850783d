FROM artifact.stage.shutterfly.com/devops/jenkins-slave-docker-deploy

RUN yum install -y \
        gcc \
        openssl \
        openssl-devel \
        zlib \
        zlib-devel \
        libffi-devel

RUN pip install setuptools --upgrade

RUN pip install \
        pyyaml \
        virtualenv

# download, compile, and install alternate version of python
RUN cd /tmp && \
    wget https://www.python.org/ftp/python/3.7.3/Python-3.7.3.tgz && \
    tar -xzf Python-3.7.3.tgz && \
    cd Python-3.7.3 && \
    ./configure --enable-optimizations && \
    make altinstall && \
    cd / && \
    rm -rf /tmp/*alenv



