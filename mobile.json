{"appName": "Mobile", "commonDimensions": {"App": "Mobile", "ManagedBy": "Mobile", "ManagedByContact": "<EMAIL>"}, "meters": [{"path": "$[?((@.eventId == 'PlaceOrderProdTime') && (@.appContext.platform == 'iOS'))]", "meterType": "counter", "meterName": "event.PlaceOrderProdTime.ios", "dimensions": {"backend_error_code": "$.appContext.backend_error_code", "errorCodeChain": "$.appContext.backend_error_code ? 'error' : 'success'"}}]}