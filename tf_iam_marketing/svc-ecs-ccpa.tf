locals {
  service_name_ecs_ccpa = "ecs-ccpa"
}

module "service_policy_svc_ecs_ccpa" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//simple_policy"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  stage_name  = var.stage_name
  policy_name = "svc-${local.service_name_ecs_ccpa}"

  template = file("${path.module}/live/sfly-aws-marketing-${var.stage_name}/templates/${var.stage_name}-svc-ecs-ccpa.json")
}

module "service_role_ecs_ccpa" {
  source = "git::**************:sflyinc-shutterfly/tf_iam_enterprise.git//role"

  account_alias = data.aws_iam_account_alias.current.account_alias
  account_id    = data.aws_caller_identity.current.account_id

  role_name_prefix = local.role_name_prefix_services
  role_name        = local.service_name_ecs_ccpa

  policy_names = [
    module.service_policy_svc_ecs_ccpa.name,
    module.standard_policies.policy_name_deny_security_critical_apis,
  ]

  # Trust policy for ECS tasks
  assume_role = "ecs-tasks_assume"
}
