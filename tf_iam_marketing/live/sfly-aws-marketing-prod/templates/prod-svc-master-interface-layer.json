{"Version": "2012-10-17", "Statement": [{"Sid": "QueueAccess", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage"], "Resource": ["arn:aws:sqs:us-east-1:${current_account_id}:prod-master-interface-layer", "arn:aws:sqs:us-east-1:${current_account_id}:prod-master-interface-layer-interactive", "arn:aws:sqs:us-east-1:${current_account_id}:prod-master-interface-layer-interactive-dlq", "arn:aws:sqs:us-east-1:${current_account_id}:prod-master-interface-layer-background", "arn:aws:sqs:us-east-1:${current_account_id}:prod-master-interface-layer-background-dlq"]}, {"Sid": "DynamoDBAccess", "Effect": "Allow", "Action": ["dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:Query"], "Resource": ["arn:aws:dynamodb:us-east-1:${current_account_id}:table/prod-master-interface-layer"]}, {"Sid": "S3ObjectMemoryDumps", "Effect": "Allow", "Action": ["s3:PutObject", "s3:AbortMultipartUpload"], "Resource": ["arn:aws:s3:${current_account_id}:prod-master-interface-layer-memory-dumps", "arn:aws:s3:${current_account_id}:prod-master-interface-layer-memory-dumps/*"]}, {"Sid": "SSMParameterStoreAccess", "Effect": "Allow", "Action": ["ssm:GetParameter"], "Resource": ["arn:aws:ssm:us-east-1:${current_account_id}:parameter/master-interface-layer/*"]}]}