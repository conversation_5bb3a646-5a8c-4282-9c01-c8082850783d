{"Version": "2012-10-17", "Statement": [{"Sid": "S3BucketAndObjectAccess", "Effect": "Allow", "Action": ["s3:ListBucket", "s3:GetObject", "s3:PutObject", "s3:DeleteObject"], "Resource": ["arn:aws:s3:::sfly-aws-mktg-${stage_name}-ccpa", "arn:aws:s3:::sfly-aws-mktg-${stage_name}-ccpa/*"]}, {"Sid": "SQSSpecificActions", "Effect": "Allow", "Action": ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:Get<PERSON>ueueAttributes", "sqs:GetQueueUrl"], "Resource": "arn:aws:sqs:us-east-1:${current_account_id}:${stage_name}-ccpa*"}, {"Sid": "SSMSpecificParameters", "Effect": "Allow", "Action": "ssm:GetParameter", "Resource": "arn:aws:ssm:us-east-1:${current_account_id}:parameter/ccpa/*"}]}