[{"path": "$[?((@.serviceLog.appContext.platform == 'iOS') && (@.SFLY_ENV == 'PROD') && (@.serviceLog.appVersion != null) && (@.serviceLog.eventId == 'smartsContainer.error.productImageRendering'))]", "meterType": "counter", "meterName": "event.smartsContainer.productImageRendering.failure", "dimensions": {"deviceId": "$.serviceLog.appContext.photosScoringDeviceID", "appVersion": "$.serviceLog.appVersion"}}, {"path": "$[?((@.serviceLog.appContext.platform == 'iOS') && (@.SFLY_ENV == 'PROD') && (@.serviceLog.appVersion != null) && (@.serviceLog.eventId == 'SFGNetworkEvent') && (@.serviceLog.resContext.status == 200))]", "meterType": "counter", "meterName": "event.SFGNetworkEvent.ok", "dimensions": {"userId": "$.serviceLog.userId", "appVersion": "$.serviceLog.appVersion"}}]